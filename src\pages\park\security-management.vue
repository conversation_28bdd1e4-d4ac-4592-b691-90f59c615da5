<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">安防管理</h1>
      <p class="page-subtitle">园区安防监控与管理</p>
    </div>
    
    <div class="page-content">
      <div class="content-grid">
        <!-- 安防统计卡片 -->
        <div class="stat-card">
          <div class="stat-icon">
            <div class="i-mdi-shield-check text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">24</div>
            <div class="stat-label">监控点位</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon online">
            <div class="i-mdi-eye text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">22</div>
            <div class="stat-label">在线摄像头</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon warning">
            <div class="i-mdi-alert text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">3</div>
            <div class="stat-label">今日告警</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon patrol">
            <div class="i-mdi-walk text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">8</div>
            <div class="stat-label">巡逻路线</div>
          </div>
        </div>
      </div>
      
      <!-- 监控点位列表 -->
      <div class="data-panel">
        <div class="panel-header">
          <h3>监控点位管理</h3>
          <div class="panel-actions">
            <button class="btn-primary">添加点位</button>
          </div>
        </div>
        <div class="panel-content">
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>点位编号</th>
                  <th>点位名称</th>
                  <th>位置</th>
                  <th>设备型号</th>
                  <th>状态</th>
                  <th>最后检查</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="camera in cameraList" :key="camera.id">
                  <td>{{ camera.code }}</td>
                  <td>{{ camera.name }}</td>
                  <td>{{ camera.location }}</td>
                  <td>{{ camera.model }}</td>
                  <td>
                    <span class="status-badge" :class="camera.status">
                      {{ camera.statusText }}
                    </span>
                  </td>
                  <td>{{ camera.lastCheck }}</td>
                  <td>
                    <div class="action-buttons">
                      <button class="btn-small">预览</button>
                      <button class="btn-small">设置</button>
                      <button class="btn-small btn-info">录像</button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      <!-- 告警记录 -->
      <div class="data-panel">
        <div class="panel-header">
          <h3>告警记录</h3>
          <div class="panel-actions">
            <button class="btn-secondary">导出记录</button>
          </div>
        </div>
        <div class="panel-content">
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>告警时间</th>
                  <th>告警类型</th>
                  <th>告警位置</th>
                  <th>告警级别</th>
                  <th>处理状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="alarm in alarmList" :key="alarm.id">
                  <td>{{ alarm.time }}</td>
                  <td>{{ alarm.type }}</td>
                  <td>{{ alarm.location }}</td>
                  <td>
                    <span class="level-badge" :class="alarm.level">
                      {{ alarm.levelText }}
                    </span>
                  </td>
                  <td>
                    <span class="status-badge" :class="alarm.status">
                      {{ alarm.statusText }}
                    </span>
                  </td>
                  <td>
                    <div class="action-buttons">
                      <button class="btn-small">查看</button>
                      <button class="btn-small btn-success">处理</button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Camera {
  id: number
  code: string
  name: string
  location: string
  model: string
  status: string
  statusText: string
  lastCheck: string
}

interface Alarm {
  id: number
  time: string
  type: string
  location: string
  level: string
  levelText: string
  status: string
  statusText: string
}

const cameraList = ref<Camera[]>([
  {
    id: 1,
    code: 'CAM001',
    name: '主入口监控',
    location: '园区大门',
    model: 'DS-2CD2T47G2-L',
    status: 'online',
    statusText: '在线',
    lastCheck: '2024-08-21 14:30'
  },
  {
    id: 2,
    code: 'CAM002',
    name: '养殖区监控',
    location: '1号养殖池',
    model: 'DS-2CD2T47G2-L',
    status: 'online',
    statusText: '在线',
    lastCheck: '2024-08-21 14:25'
  },
  {
    id: 3,
    code: 'CAM003',
    name: '办公楼监控',
    location: '办公楼大厅',
    model: 'DS-2CD2T47G2-L',
    status: 'offline',
    statusText: '离线',
    lastCheck: '2024-08-21 10:15'
  }
])

const alarmList = ref<Alarm[]>([
  {
    id: 1,
    time: '2024-08-21 13:45',
    type: '异常入侵',
    location: '园区围墙东侧',
    level: 'high',
    levelText: '高级',
    status: 'pending',
    statusText: '待处理'
  },
  {
    id: 2,
    time: '2024-08-21 12:30',
    type: '设备离线',
    location: 'CAM003',
    level: 'medium',
    levelText: '中级',
    status: 'processing',
    statusText: '处理中'
  },
  {
    id: 3,
    time: '2024-08-21 11:15',
    type: '移动检测',
    location: '仓库区域',
    level: 'low',
    levelText: '低级',
    status: 'resolved',
    statusText: '已处理'
  }
])
</script>

<style scoped>
.page-container {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
  background: linear-gradient(135deg, #001122 0%, #003366 50%, #004488 100%);
}

.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #66ccff;
  text-shadow: 0 0 20px rgba(102, 204, 255, 0.8);
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 16px;
  color: rgba(102, 204, 255, 0.7);
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: rgba(102, 204, 255, 0.1);
  border: 1px solid rgba(102, 204, 255, 0.3);
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  backdrop-filter: blur(10px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(102, 204, 255, 0.2);
  color: #66ccff;
}

.stat-icon.online {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.stat-icon.warning {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.stat-icon.patrol {
  background: rgba(168, 85, 247, 0.2);
  color: #a855f7;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #66ccff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: rgba(102, 204, 255, 0.7);
}

.data-panel {
  background: rgba(102, 204, 255, 0.05);
  border: 1px solid rgba(102, 204, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  margin-bottom: 24px;
}

.panel-header {
  padding: 20px 24px;
  border-bottom: 1px solid rgba(102, 204, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #66ccff;
}

.btn-primary, .btn-secondary {
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #66ccff, #33aaff);
  color: #001122;
}

.btn-secondary {
  background: rgba(102, 204, 255, 0.1);
  color: #66ccff;
  border: 1px solid rgba(102, 204, 255, 0.3);
}

.btn-primary:hover, .btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 204, 255, 0.3);
}

.table-container {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid rgba(102, 204, 255, 0.1);
}

.data-table th {
  background: rgba(102, 204, 255, 0.1);
  color: #66ccff;
  font-weight: 600;
}

.data-table td {
  color: rgba(102, 204, 255, 0.9);
}

.status-badge, .level-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge.online {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.status-badge.offline {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.status-badge.pending {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.status-badge.processing {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.status-badge.resolved {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.level-badge.high {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.level-badge.medium {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.level-badge.low {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-small {
  padding: 4px 12px;
  border: 1px solid rgba(102, 204, 255, 0.3);
  background: rgba(102, 204, 255, 0.1);
  color: #66ccff;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-small:hover {
  background: rgba(102, 204, 255, 0.2);
}

.btn-small.btn-info {
  border-color: rgba(168, 85, 247, 0.3);
  background: rgba(168, 85, 247, 0.1);
  color: #a855f7;
}

.btn-small.btn-success {
  border-color: rgba(34, 197, 94, 0.3);
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.btn-small.btn-info:hover {
  background: rgba(168, 85, 247, 0.2);
}

.btn-small.btn-success:hover {
  background: rgba(34, 197, 94, 0.2);
}
</style>
