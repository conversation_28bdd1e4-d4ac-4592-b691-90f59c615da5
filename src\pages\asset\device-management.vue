<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">设备管理</h1>
      <p class="page-subtitle">设备信息管理与监控</p>
    </div>
    
    <div class="page-content">
      <div class="content-grid">
        <!-- 设备统计卡片 -->
        <div class="stat-card">
          <div class="stat-icon">
            <div class="i-mdi-cog text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">156</div>
            <div class="stat-label">总设备数</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon online">
            <div class="i-mdi-check-circle text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">142</div>
            <div class="stat-label">在线设备</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon warning">
            <div class="i-mdi-alert-circle text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">8</div>
            <div class="stat-label">故障设备</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon offline">
            <div class="i-mdi-close-circle text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">6</div>
            <div class="stat-label">离线设备</div>
          </div>
        </div>
      </div>
      
      <!-- 设备列表 -->
      <div class="data-panel">
        <div class="panel-header">
          <h3>设备列表</h3>
          <div class="panel-actions">
            <button class="btn-primary">添加设备</button>
          </div>
        </div>
        <div class="panel-content">
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>设备编号</th>
                  <th>设备名称</th>
                  <th>设备类型</th>
                  <th>状态</th>
                  <th>位置</th>
                  <th>最后维护</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="device in deviceList" :key="device.id">
                  <td>{{ device.code }}</td>
                  <td>{{ device.name }}</td>
                  <td>{{ device.type }}</td>
                  <td>
                    <span class="status-badge" :class="device.status">
                      {{ device.statusText }}
                    </span>
                  </td>
                  <td>{{ device.location }}</td>
                  <td>{{ device.lastMaintenance }}</td>
                  <td>
                    <div class="action-buttons">
                      <button class="btn-small">编辑</button>
                      <button class="btn-small btn-danger">删除</button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Device {
  id: number
  code: string
  name: string
  type: string
  status: string
  statusText: string
  location: string
  lastMaintenance: string
}

const deviceList = ref<Device[]>([
  {
    id: 1,
    code: 'DEV001',
    name: '增氧机A1',
    type: '增氧设备',
    status: 'online',
    statusText: '正常',
    location: '1号池',
    lastMaintenance: '2024-08-15'
  },
  {
    id: 2,
    code: 'DEV002',
    name: '水质监测仪B1',
    type: '监测设备',
    status: 'warning',
    statusText: '告警',
    location: '2号池',
    lastMaintenance: '2024-08-10'
  },
  {
    id: 3,
    code: 'DEV003',
    name: '投料机C1',
    type: '投料设备',
    status: 'offline',
    statusText: '离线',
    location: '3号池',
    lastMaintenance: '2024-08-05'
  }
])
</script>

<style scoped>
.page-container {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
  background: linear-gradient(135deg, #001122 0%, #003366 50%, #004488 100%);
}

.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #66ccff;
  text-shadow: 0 0 20px rgba(102, 204, 255, 0.8);
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 16px;
  color: rgba(102, 204, 255, 0.7);
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: rgba(102, 204, 255, 0.1);
  border: 1px solid rgba(102, 204, 255, 0.3);
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  backdrop-filter: blur(10px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(102, 204, 255, 0.2);
  color: #66ccff;
}

.stat-icon.online {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.stat-icon.warning {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.stat-icon.offline {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #66ccff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: rgba(102, 204, 255, 0.7);
}

.data-panel {
  background: rgba(102, 204, 255, 0.05);
  border: 1px solid rgba(102, 204, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.panel-header {
  padding: 20px 24px;
  border-bottom: 1px solid rgba(102, 204, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #66ccff;
}

.btn-primary {
  background: linear-gradient(135deg, #66ccff, #33aaff);
  color: #001122;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 204, 255, 0.3);
}

.table-container {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid rgba(102, 204, 255, 0.1);
}

.data-table th {
  background: rgba(102, 204, 255, 0.1);
  color: #66ccff;
  font-weight: 600;
}

.data-table td {
  color: rgba(102, 204, 255, 0.9);
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge.online {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.status-badge.warning {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.status-badge.offline {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-small {
  padding: 4px 12px;
  border: 1px solid rgba(102, 204, 255, 0.3);
  background: rgba(102, 204, 255, 0.1);
  color: #66ccff;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-small:hover {
  background: rgba(102, 204, 255, 0.2);
}

.btn-small.btn-danger {
  border-color: rgba(239, 68, 68, 0.3);
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.btn-small.btn-danger:hover {
  background: rgba(239, 68, 68, 0.2);
}
</style>
