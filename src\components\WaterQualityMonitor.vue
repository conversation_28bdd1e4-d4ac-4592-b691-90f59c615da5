<template>
  <div class="water-quality-monitor">
    <!-- 水质状态指示器 -->
    <div class="quality-status-bar">
      <div class="status-indicators">
        <div
          v-for="config in chartConfigs"
          :key="config.key"
          class="status-indicator"
          :class="{
            'status-normal': isParameterNormal(config.key),
            'status-warning': !isParameterNormal(config.key),
          }"
        >
          <div
            class="indicator-dot"
            :style="{ backgroundColor: config.color }"
          ></div>
          <span class="indicator-label">{{ config.name }}</span>
          <span class="indicator-value" :style="{ color: config.color }">
            {{ getCurrentValue(config.key) }}{{ config.unit }}
          </span>
        </div>
      </div>
      <div class="overall-status" :class="overallStatus.class">
        <div class="status-icon">{{ overallStatus.icon }}</div>
        <span class="status-text">{{ overallStatus.text }}</span>
      </div>
    </div>

    <!-- 图表组件 -->
    <div class="chart-container">
      <EChartsComponent :option="chartOption" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from "vue";
import EChartsComponent from "./EChartsComponent.vue";
import type { EChartsOption } from "echarts";

interface Props {
  buildingId: number;
}

const props = defineProps<Props>();

// 水质监测参数配置 - 使用不同颜色区分不同检测目标
const chartConfigs = [
  {
    name: "PH值",
    key: "ph",
    color: "#00ff88", // 绿色 - 表示酸碱平衡
    unit: "",
    yAxisIndex: 0,
    normalRange: [7.0, 8.5], // 正常范围
    description: "水体酸碱度",
  },
  {
    name: "水温(°C)",
    key: "temperature",
    color: "#ff6b35", // 橙红色 - 表示温度
    unit: "°C",
    yAxisIndex: 0,
    normalRange: [24, 30], // 正常范围
    description: "水体温度",
  },
  {
    name: "溶解氧(mg/L)",
    key: "oxygen",
    color: "#4dabf7", // 蓝色 - 表示氧气
    unit: "mg/L",
    yAxisIndex: 0,
    normalRange: [5.0, 8.0], // 正常范围
    description: "水中溶解氧含量",
  },
  {
    name: "亚硝酸盐(mg/L)",
    key: "nitrite",
    color: "#ff4757", // 红色 - 表示有害物质
    unit: "mg/L",
    yAxisIndex: 1,
    normalRange: [0, 0.5], // 正常范围
    description: "有害亚硝酸盐含量",
  },
  {
    name: "总盐(‰)",
    key: "salinity",
    color: "#a55eea", // 紫色 - 表示盐度
    unit: "‰",
    yAxisIndex: 1,
    normalRange: [12, 18], // 正常范围
    description: "水体盐度",
  },
  {
    name: "氨氮(mg/L)",
    key: "ammonia",
    color: "#ffa502", // 黄色 - 表示氨氮
    unit: "mg/L",
    yAxisIndex: 1,
    normalRange: [0, 0.3], // 正常范围
    description: "氨氮含量",
  },
];

// 生成模拟数据
const generateWaterQualityData = (buildingId: number) => {
  const now = new Date();
  const data: Record<string, Array<{ time: string; value: number }>> = {};

  chartConfigs.forEach((config) => {
    data[config.key] = [];

    for (let i = 23; i >= 0; i--) {
      // 创建整点时间
      const time = new Date(now.getTime() - i * 60 * 60 * 1000);
      time.setMinutes(0, 0, 0); // 设置为整点
      let value = 0;

      // 根据楼栋ID和参数类型生成不同的基础值
      const baseOffset = (buildingId - 1) * 0.1;

      switch (config.key) {
        case "temperature":
          // 水温：24-30°C 正常范围，模拟日夜温差
          value = 27 + Math.sin(i * 0.3) * 2 + Math.random() * 1 + baseOffset;
          break;
        case "ph":
          // PH值：7.0-8.5 正常范围
          value =
            7.8 +
            Math.sin(i * 0.2) * 0.4 +
            Math.random() * 0.3 +
            baseOffset * 0.1;
          break;
        case "salinity":
          // 总盐：12-18‰ 正常范围
          value =
            15 + Math.sin(i * 0.4) * 1.5 + Math.random() * 0.8 + baseOffset;
          break;
        case "oxygen":
          // 溶解氧：5.0-8.0 mg/L 正常范围
          value =
            6.5 +
            Math.sin(i * 0.5) * 1.0 +
            Math.random() * 0.5 +
            baseOffset * 0.3;
          break;
        case "nitrite":
          // 亚硝酸盐：0-0.5 mg/L 正常范围，有害物质应保持低水平
          value =
            0.2 +
            Math.sin(i * 0.6) * 0.1 +
            Math.random() * 0.15 +
            baseOffset * 0.02;
          break;
        case "ammonia":
          // 氨氮：0-0.3 mg/L 正常范围，有害物质应保持低水平
          value =
            0.15 +
            Math.sin(i * 0.7) * 0.08 +
            Math.random() * 0.1 +
            baseOffset * 0.01;
          break;
      }

      data[config.key].push({
        time: time.toLocaleTimeString("zh-CN", {
          hour: "2-digit",
          minute: "2-digit",
        }),
        value: Number(value.toFixed(2)),
      });
    }
  });

  return data;
};

// 获取当前数据
const currentData = computed(() => generateWaterQualityData(props.buildingId));

// 获取当前参数值
const getCurrentValue = (key: string) => {
  const data = currentData.value[key];
  return data && data.length > 0 ? data[data.length - 1].value : 0;
};

// 检查参数是否正常
const isParameterNormal = (key: string) => {
  const config = chartConfigs.find((c) => c.key === key);
  if (!config) return true;

  const value = getCurrentValue(key);
  const [min, max] = config.normalRange;
  return value >= min && value <= max;
};

// 整体状态
const overallStatus = computed(() => {
  const normalCount = chartConfigs.filter((config) =>
    isParameterNormal(config.key)
  ).length;
  const totalCount = chartConfigs.length;
  const normalRatio = normalCount / totalCount;

  if (normalRatio === 1) {
    return {
      class: "status-excellent",
      icon: "🟢",
      text: "水质优良",
    };
  } else if (normalRatio >= 0.8) {
    return {
      class: "status-good",
      icon: "🟡",
      text: "水质良好",
    };
  } else if (normalRatio >= 0.6) {
    return {
      class: "status-fair",
      icon: "🟠",
      text: "水质一般",
    };
  } else {
    return {
      class: "status-poor",
      icon: "🔴",
      text: "水质异常",
    };
  }
});

// 生成合并图表选项的计算属性
const chartOption = computed((): EChartsOption => {
  const data = generateWaterQualityData(props.buildingId);

  // 获取时间轴数据（所有参数的时间都相同）
  const timeData = data[chartConfigs[0].key].map((item) => item.time);

  // 创建Y轴配置 - 参考 aquaculture-dashboard2.vue 的简化方式
  const yAxes = [
    {
      type: "value" as const,
      nameTextStyle: { color: "#0efcff", fontSize: 10 },
      axisLine: {
        show: true,
        lineStyle: { color: "#0efcff", width: 3, type: "solid" as const },
      },
      axisLabel: {
        color: "#0efcff",
        fontSize: 10,
        fontWeight: "bold" as const,
      },
      splitLine: { lineStyle: { color: "rgba(14, 252, 255, 0.2)" } },
    },
    {
      type: "value" as const,
      nameTextStyle: { color: "#0efcff", fontSize: 10 },
      axisLine: {
        show: true,
        lineStyle: { color: "#0efcff", width: 3, type: "solid" as const },
      },
      axisLabel: {
        color: "#0efcff",
        fontSize: 10,
        fontWeight: "bold" as const,
      },
      splitLine: { show: false },
    },
  ];

  // 创建系列数据 - 增强视觉效果
  const series = chartConfigs.map((config) => ({
    name: config.name,
    type: "line" as const,
    yAxisIndex: config.yAxisIndex,
    data: data[config.key].map((item, index) => {
      const value = item.value;
      const [min, max] = config.normalRange;
      // 判断数值是否在正常范围内
      const isNormal = value >= min && value <= max;
      return {
        value: value,
        itemStyle: {
          color: isNormal ? config.color : "#ff4757", // 异常值用红色标记
          borderColor: isNormal ? config.color : "#ff4757",
          borderWidth: isNormal ? 1 : 2,
        },
      };
    }),
    smooth: true,
    lineStyle: {
      color: config.color,
      width: 3,
      shadowColor: config.color,
      shadowBlur: 8,
      shadowOffsetY: 2,
    },
    itemStyle: {
      color: config.color,
      borderColor: config.color,
      borderWidth: 1,
    },
    symbol: "circle",
    symbolSize: 6,
    emphasis: {
      scale: true,
      itemStyle: {
        borderWidth: 3,
        shadowBlur: 10,
        shadowColor: config.color,
      },
    },
    areaStyle: {
      color: {
        type: "linear",
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          { offset: 0, color: config.color + "40" }, // 40% 透明度
          { offset: 1, color: config.color + "10" }, // 10% 透明度
        ],
      },
    },
  }));

  return {
    backgroundColor: "transparent",
    grid: { left: 30, right: 30, top: 60, bottom: 30 },
    legend: {
      data: chartConfigs.map((config) => ({
        name: config.name,
        textStyle: {
          color: config.color,
          fontSize: 12,
          fontWeight: "bold",
        },
        icon: "circle",
      })),
      textStyle: {
        color: "#0efcff",
        fontSize: 12,
        fontWeight: "bold",
      },
      top: 5,
      left: "center",
      itemGap: 20,
      itemWidth: 12,
      itemHeight: 12,
      backgroundColor: "rgba(14, 252, 255, 0.05)",
      borderColor: "rgba(14, 252, 255, 0.3)",
      borderWidth: 1,
      borderRadius: 6,
      padding: [8, 16],
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 20, 40, 0.95)",
      borderColor: "#0efcff",
      borderWidth: 2,
      borderRadius: 8,
      textStyle: {
        color: "#0efcff",
        fontSize: 13,
        fontWeight: "bold",
      },
      extraCssText: "box-shadow: 0 4px 12px rgba(14, 252, 255, 0.3);",
      formatter: (params: any) => {
        const paramArray = Array.isArray(params) ? params : [params];
        let result = `<div style="font-size: 14px; color: #66ccff; margin-bottom: 8px; border-bottom: 1px solid #0efcff; padding-bottom: 4px;">
          <strong>⏰ ${paramArray[0].axisValue}</strong>
        </div>`;

        paramArray.forEach((param: any) => {
          const config = chartConfigs.find((c) => c.name === param.seriesName);
          if (config) {
            const value =
              typeof param.value === "object" ? param.value.value : param.value;
            const [min, max] = config.normalRange;
            const isNormal = value >= min && value <= max;
            const statusIcon = isNormal ? "✅" : "⚠️";
            const statusText = isNormal ? "正常" : "异常";
            const statusColor = isNormal ? "#00ff88" : "#ff4757";

            result += `<div style="margin: 6px 0; padding: 4px; background: rgba(14, 252, 255, 0.1); border-radius: 4px;">
              ${param.marker}
              <span style="color: ${config.color}; font-weight: bold;">${param.seriesName}</span>:
              <span style="color: ${statusColor}; font-weight: bold;">${value}${config.unit}</span>
              <span style="color: ${statusColor}; margin-left: 8px;">${statusIcon} ${statusText}</span>
              <br/>
              <span style="font-size: 11px; color: #99ccff; margin-left: 16px;">
                ${config.description} (正常范围: ${min}-${max}${config.unit})
              </span>
            </div>`;
          }
        });
        return result;
      },
    },
    xAxis: {
      type: "category",
      data: timeData,
      axisLine: {
        show: true,
        lineStyle: { color: "#0efcff", width: 3, type: "solid" },
      },
      axisLabel: {
        color: "#0efcff",
        fontSize: 10,
        interval: 2,
        margin: 8,
        fontWeight: "bold",
      },
    },
    yAxis: yAxes,
    series: series,
  };
});

// 监听楼栋变化，重新生成图表选项
watch(
  () => props.buildingId,
  () => {
    // 计算属性会自动重新计算
  },
  { immediate: false }
);
</script>

<style scoped>
.water-quality-monitor {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quality-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: linear-gradient(
    135deg,
    rgba(14, 252, 255, 0.1) 0%,
    rgba(14, 252, 255, 0.05) 100%
  );
  border: 1px solid rgba(14, 252, 255, 0.3);
  border-radius: 8px;
  backdrop-filter: blur(5px);
}

.status-indicators {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
  background: rgba(14, 252, 255, 0.05);
  border: 1px solid transparent;
}

.status-indicator.status-normal {
  border-color: rgba(0, 255, 136, 0.3);
  background: rgba(0, 255, 136, 0.1);
}

.status-indicator.status-warning {
  border-color: rgba(255, 71, 87, 0.3);
  background: rgba(255, 71, 87, 0.1);
  animation: warningPulse 2s infinite;
}

@keyframes warningPulse {
  0%,
  100% {
    box-shadow: 0 0 5px rgba(255, 71, 87, 0.3);
  }
  50% {
    box-shadow: 0 0 15px rgba(255, 71, 87, 0.6);
  }
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  box-shadow: 0 0 8px currentColor;
  animation: dotPulse 2s infinite;
}

@keyframes dotPulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

.indicator-label {
  font-size: 11px;
  color: #66ccff;
  font-weight: 500;
  white-space: nowrap;
}

.indicator-value {
  font-size: 12px;
  font-weight: bold;
  white-space: nowrap;
}

.overall-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 8px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.status-excellent {
  background: linear-gradient(
    135deg,
    rgba(0, 255, 136, 0.2) 0%,
    rgba(0, 255, 136, 0.1) 100%
  );
  border: 1px solid rgba(0, 255, 136, 0.4);
  color: #00ff88;
}

.status-good {
  background: linear-gradient(
    135deg,
    rgba(255, 193, 7, 0.2) 0%,
    rgba(255, 193, 7, 0.1) 100%
  );
  border: 1px solid rgba(255, 193, 7, 0.4);
  color: #ffc107;
}

.status-fair {
  background: linear-gradient(
    135deg,
    rgba(255, 152, 0, 0.2) 0%,
    rgba(255, 152, 0, 0.1) 100%
  );
  border: 1px solid rgba(255, 152, 0, 0.4);
  color: #ff9800;
}

.status-poor {
  background: linear-gradient(
    135deg,
    rgba(255, 71, 87, 0.2) 0%,
    rgba(255, 71, 87, 0.1) 100%
  );
  border: 1px solid rgba(255, 71, 87, 0.4);
  color: #ff4757;
  animation: statusAlert 2s infinite;
}

@keyframes statusAlert {
  0%,
  100% {
    box-shadow: 0 0 10px rgba(255, 71, 87, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 71, 87, 0.6);
  }
}

.status-icon {
  font-size: 16px;
  animation: iconBounce 2s infinite;
}

@keyframes iconBounce {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.status-text {
  font-size: 13px;
  white-space: nowrap;
}

.chart-container {
  flex: 1;
  height: 0;
  min-height: 200px;
}
</style>
